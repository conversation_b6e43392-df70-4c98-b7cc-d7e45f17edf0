# 期末复习平台项目

## 项目概述
期末复习平台是一个专为学生设计的智能复习系统，旨在提供个性化的学习体验和高效的复习管理。

## 项目结构

```
qimo/
├── docs/                    # 项目文档目录
│   ├── prd/                # 产品需求文档 (Emma负责)
│   ├── architecture/       # 系统架构文档 (Bob负责)
│   ├── development/        # 开发技术文档 (Alex负责)
│   ├── analytics/          # 数据分析文档 (David负责)
│   ├── tasks/             # 任务规划文档
│   └── templates/         # 文档模板库
├── src/                    # 源代码目录
├── tests/                  # 测试文件目录
├── config/                 # 配置文件目录
└── README.md              # 项目说明文档
```

## 团队分工

- **Mike (团队领袖)**: 项目管理、任务协调、决策制定
- **Emma (产品经理)**: 需求分析、PRD编写、用户体验设计
- **Bob (架构师)**: 系统架构设计、技术选型、架构文档
- **Alex (工程师)**: 代码实现、测试开发、技术文档
- **David (数据分析师)**: 数据分析、指标定义、分析报告

## 开发规范

### 文档管理
- 所有文档必须完整生成并保存到对应目录
- 使用Markdown格式，遵循统一命名规范
- 重要文档需要版本控制和变更记录

### 技术规范
- 统一使用Context7环境进行开发
- 所有浏览器相关操作必须使用Playwright工具
- 严格执行测试驱动开发(TDD)流程
- 测试通过后自动清理临时测试文件

### 协作流程
1. Mike分解任务 → Emma输出PRD → Bob架构设计 → Alex代码实现
2. 每个阶段都必须产出完整的文档
3. 质量门禁：PRD审查 → 架构审查 → 代码审查 → 数据验证

## 项目状态
- [x] 项目初始化
- [ ] 需求分析
- [ ] 架构设计
- [ ] 功能开发
- [ ] 测试验证
- [ ] 部署上线

## 版权声明
本项目所有产出（代码、文档、分析报告等）的最终版权归属于【随影】。

---
*项目创建时间: 2025-01-27*
*团队领袖: Mike*