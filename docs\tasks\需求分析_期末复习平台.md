# 期末复习平台需求分析文档

## 文档信息
- **分析时间**: 2025-01-27
- **分析人员**: Emma (产品经理)
- **草案来源**: 老板提供的技术规范文档
- **分析目的**: 为PRD重塑提供完整的需求基础

## 1. 项目概述分析

### 1.1 产品定位
- **产品名称**: 期末复习平台
- **核心价值**: 为管理员提供便捷的、支持多学科、可长久保存的在线Markdown笔记阅读平台
- **开放策略**: 对所有知道网址的访客开放只读访问

### 1.2 业务背景推断
- **目标场景**: 期末复习阶段的知识管理和分享
- **核心痛点**: 
  - 复习资料分散，难以统一管理
  - 缺乏支持多学科分类的笔记平台
  - 需要长期保存和便捷访问的解决方案
  - 希望能够分享给其他学习者

## 2. 用户角色分析

### 2.1 管理员角色
- **身份特征**: 笔记的创建者和管理者（老板本人）
- **访问方式**: 通过特定管理路径 `/admin-panel-abcdef` 访问
- **核心需求**:
  - 创建和管理学科分类
  - 批量上传文件夹（保持目录结构）
  - 覆盖式更新已有内容
  - 删除不需要的学科或文件
- **使用场景**:
  - 整理期末复习资料
  - 按学科分类上传笔记
  - 定期更新和维护内容

### 2.2 访客角色
- **身份特征**: 其他学习者或需要查阅资料的人
- **访问方式**: 通过网站根路径 `/` 访问
- **核心需求**:
  - 浏览所有学科列表
  - 查看和阅读Markdown笔记
  - 查看笔记中的图片
  - 在不同设备上良好的阅读体验
- **使用场景**:
  - 期末复习时查阅相关资料
  - 学习参考他人的笔记整理
  - 移动端随时查看复习内容

## 3. 核心功能分析

### 3.1 学科管理功能
- **FRD-01: 创建学科**
  - 业务价值: 提供清晰的知识分类体系
  - 用户操作: 管理员点击"新建学科" -> 输入名称 -> 确认
  - 系统反馈: 学科列表出现新卡片

- **FRD-07: 内容删除**
  - 业务价值: 维护内容的时效性和准确性
  - 用户操作: 删除整个学科或单个文件/文件夹
  - 安全机制: 二次确认提示

### 3.2 文件管理功能
- **FRD-02: 文件夹整体上传与更新**
  - 业务价值: 批量管理，提高效率
  - 核心特性: 支持包含.md文件和图片的文件夹上传
  - 更新逻辑: 同名文件夹执行覆盖式更新
  - 安全机制: 覆盖前二次确认

- **FRD-03: 文件结构保留**
  - 业务价值: 保持原有的组织逻辑
  - 技术要求: 完整保留子文件夹层级结构

### 3.3 内容展示功能
- **FRD-04: Markdown与图片正确显示**
  - 业务价值: 提供良好的阅读体验
  - 技术要求: 正确渲染Markdown语法
  - 图片处理: 自动处理相对路径引用

- **FRD-05: 访客浏览**
  - 业务价值: 开放式知识分享
  - 访问控制: 免登录浏览
  - 排序规则: 文件夹优先，字母顺序排序

- **FRD-06: 响应式布局**
  - 业务价值: 多设备良好体验
  - 适配范围: 桌面、平板、手机端

## 4. 技术约束分析

### 4.1 技术栈要求
- **前端**: Vue3 + TypeScript + Ant Design Vue + UnoCSS + Vite
- **基础框架**: Vben Admin（剥离登录、权限、动态菜单系统）
- **后端**: Node.js + Koa (或 Express)
- **数据库**: SQLite (通过 better-sqlite3 操作)
- **部署**: Docker + docker-compose + PM2 + Nginx

### 4.2 存储架构
- **存储位置**: `/storage/data/` 目录
- **目录结构**: 按学科ID分离 (`/storage/data/{subject_id}/`)
- **数据持久化**: Docker Volumes挂载

### 4.3 性能限制
- **上传限制**: 单个学科单次上传总体积上限500MB
- **文件限制**: 单个文件大小上限20MB
- **文件类型**: 仅接受.md文件和指定格式图片

### 4.4 安全要求
- **管理访问**: 通过固定URL路径实现访问控制
- **文件过滤**: 自动忽略不支持的文件类型
- **环境配置**: 管理URL作为环境变量配置

## 5. API设计要求

### 5.1 核心接口
- `POST /api/subjects` - 创建新学科
- `GET /api/subjects` - 获取所有学科列表
- `DELETE /api/subjects/{id}` - 删除指定学科
- `GET /api/subjects/{id}/files` - 获取指定学科的文件结构
- `POST /api/subjects/{id}/upload` - 上传文件夹到指定学科
- `GET /api/files/{fileId}` - 获取单个md文件内容
- `DELETE /api/file_nodes/{id}` - 删除单个文件或文件夹
- `GET /api/assets/{file_node_id}` - 获取图片等静态资源

## 6. 数据库模型要求

### 6.1 subjects表
- 存储学科基本信息
- 支持唯一性约束和级联删除

### 6.2 file_nodes表
- 存储文件和文件夹的树形结构
- 支持父子关系和相对路径存储

## 7. 关键业务逻辑

### 7.1 原子化上传流程
- 临时目录暂存 -> 数据库事务 -> 文件移动 -> 提交/回滚
- 确保数据一致性，避免部分成功的脏数据

### 7.2 Markdown图片处理
- 自动识别相对路径图片引用
- 替换为服务器访问URL格式
- 确保图片正常显示

## 8. 需求优先级分析

### 8.1 P0 - 核心功能（必须实现）
- 学科创建和管理
- 文件上传和结构保留
- Markdown渲染和图片显示
- 访客浏览功能

### 8.2 P1 - 重要功能（应该实现）
- 覆盖更新功能
- 内容删除功能
- 响应式布局

### 8.3 P2 - 优化功能（可以实现）
- 高级排序和搜索
- 性能优化
- 用户体验增强

## 9. 风险识别

### 9.1 技术风险
- 大文件上传的性能问题
- 并发上传的数据一致性
- 图片路径替换的准确性

### 9.2 业务风险
- 用户误操作导致数据丢失
- 访问控制的安全性
- 长期存储的可靠性

## 10. 总结

本次需求分析基于老板提供的技术规范文档，提取了完整的业务需求和技术约束。核心价值在于为期末复习提供一个便捷的、多学科的、可长久保存的笔记管理和分享平台。

**关键成功因素**:
1. 简单易用的管理界面
2. 可靠的文件上传和存储机制
3. 良好的Markdown阅读体验
4. 稳定的系统架构和部署方案

---
*分析完成时间: 2025-01-27*
*下一步: 补充PRD标准章节内容*