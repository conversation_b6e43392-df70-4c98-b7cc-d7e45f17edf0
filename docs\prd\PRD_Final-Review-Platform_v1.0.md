# PRD - 期末复习平台 V1.0

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **产品名称** | 期末复习平台 (Final Review Platform) |
| **版本** | v1.0 |
| **创建时间** | 2025-01-27 |
| **负责人** | Emma (产品经理) |
| **审核人** | Mike (团队领袖) |
| **版权归属** | 随影 |

### 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-27 | 初始版本，基于需求分析文档重塑生成 | Emma |

## 2. 背景与问题陈述

### 2.1 项目背景

在期末复习阶段，学生和教育者面临着知识管理和分享的挑战。传统的复习资料管理方式存在分散、难以分类、不便分享等问题，急需一个统一的、支持多学科分类的在线笔记管理和阅读平台。

### 2.2 问题陈述

**核心痛点**：
1. **资料分散问题**：复习资料散落在不同位置，难以统一管理和快速查找
2. **分类管理困难**：缺乏支持多学科分类的专业笔记管理工具
3. **分享不便**：现有工具难以实现便捷的知识分享和协作学习
4. **长期保存需求**：需要可靠的长期存储解决方案，确保资料不丢失
5. **多设备访问**：需要在不同设备上都能获得良好的阅读体验

### 2.3 机会分析

- **市场需求**：期末复习是每个学期的刚需场景，用户基数大
- **技术成熟**：Markdown格式已成为知识管理的标准，技术实现相对成熟
- **差异化优势**：专注于期末复习场景，提供简单易用的管理和分享功能

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)

**O1 - 提升复习效率**：为用户提供高效的多学科笔记管理和阅读平台
**O2 - 促进知识分享**：建立开放的知识分享机制，促进协作学习
**O3 - 确保数据安全**：提供可靠的数据存储和访问控制机制

### 3.2 关键结果 (Key Results)

**KR1 - 用户使用指标**：
- 管理员能够在5分钟内完成新学科创建和首次文件上传
- 访客能够在3秒内加载并浏览任意学科的笔记列表
- 单个Markdown文件的渲染和显示时间不超过2秒

**KR2 - 系统性能指标**：
- 支持单次上传最大500MB的文件夹
- 系统可用性达到99.5%以上
- 支持至少100个并发访客同时浏览

**KR3 - 功能完整性指标**：
- 100%支持标准Markdown语法渲染
- 100%保留上传文件的目录结构
- 支持jpg、png、gif、svg、webp等主流图片格式

### 3.3 反向指标 (Counter Metrics)

- **系统复杂度**：管理界面操作步骤不超过3步
- **学习成本**：新用户无需培训即可完成基本操作
- **资源消耗**：服务器资源使用率不超过80%

## 4. 用户画像与用户故事

### 4.1 目标用户画像

#### 主要用户：管理员
- **身份特征**：教师、学习小组组长、知识整理者
- **技术水平**：具备基本的计算机操作能力，熟悉Markdown格式
- **使用动机**：希望高效管理和分享复习资料
- **痛点**：资料管理繁琐，分享不便

#### 次要用户：访客
- **身份特征**：学生、学习者、知识查阅者
- **技术水平**：基本的网页浏览能力
- **使用动机**：查找和学习复习资料
- **痛点**：资料查找困难，阅读体验不佳

### 4.2 用户故事

#### 管理员用户故事

**US-01 学科管理**
> 作为一名管理员，我希望能够创建不同的学科分类，以便按照课程或主题来组织我的复习资料。

**US-02 批量上传**
> 作为一名管理员，我希望能够一次性上传整个文件夹，包括其中的Markdown文件和图片，以便快速建立完整的知识库。

**US-03 内容更新**
> 作为一名管理员，我希望能够覆盖更新已有的文件夹内容，以便及时更新过时的复习资料。

**US-04 内容管理**
> 作为一名管理员，我希望能够删除不需要的学科或文件，以便保持资料库的整洁和时效性。

#### 访客用户故事

**US-05 浏览学科**
> 作为一名访客，我希望能够浏览所有可用的学科列表，以便找到我需要的复习资料。

**US-06 阅读笔记**
> 作为一名访客，我希望能够清晰地阅读Markdown格式的笔记，包括其中的图片和格式，以便获得良好的学习体验。

**US-07 多设备访问**
> 作为一名访客，我希望能够在手机、平板和电脑上都能正常浏览和阅读，以便随时随地进行复习。

## 5. 功能规格详述

### 5.1 核心功能流程图

```
管理员流程：
访问管理URL → 创建学科 → 上传文件夹 → 系统处理 → 内容发布

访客流程：
访问首页 → 选择学科 → 浏览文件列表 → 查看具体内容
```

### 5.2 详细功能规格

#### 功能模块1：学科管理
- **FRD-01 创建学科**
  - 输入：学科名称（必填，唯一性校验）
  - 处理：创建数据库记录，生成学科ID
  - 输出：学科卡片显示在管理界面
  - 异常处理：重名提示，输入验证

- **FRD-07 删除学科**
  - 输入：学科ID
  - 处理：二次确认 → 删除数据库记录和文件
  - 输出：学科从列表中移除
  - 异常处理：确认对话框，级联删除

#### 功能模块2：文件管理
- **FRD-02 文件夹上传**
  - 输入：文件夹（包含.md和图片文件）
  - 处理：原子化上传流程，保持目录结构
  - 输出：文件树结构显示
  - 异常处理：文件类型过滤，大小限制检查

- **FRD-03 结构保留**
  - 输入：多层级文件夹结构
  - 处理：递归解析目录，建立父子关系
  - 输出：完整复现原始目录结构
  - 异常处理：路径冲突处理

#### 功能模块3：内容展示
- **FRD-04 Markdown渲染**
  - 输入：.md文件内容
  - 处理：Markdown解析，图片路径替换
  - 输出：格式化的HTML内容
  - 异常处理：语法错误容错，图片加载失败处理

- **FRD-05 访客浏览**
  - 输入：访问请求
  - 处理：权限验证，内容排序
  - 输出：学科列表和文件列表
  - 异常处理：无内容提示，加载失败重试

- **FRD-06 响应式布局**
  - 输入：不同设备的访问请求
  - 处理：CSS媒体查询适配
  - 输出：适配的界面布局
  - 异常处理：降级显示方案

### 5.3 业务逻辑规则

1. **文件类型限制**：仅接受.md文件和jpg、jpeg、png、gif、svg、webp格式图片
2. **大小限制**：单个文件≤20MB，单次上传总量≤500MB
3. **排序规则**：文件夹优先，然后按字母顺序(A-Z)排序
4. **覆盖规则**：同名文件夹执行覆盖式更新，需二次确认
5. **访问控制**：管理功能通过特定URL路径控制访问

### 5.4 边缘情况与异常处理

1. **上传失败**：原子化操作，失败时完全回滚，不产生脏数据
2. **图片加载失败**：显示alt文本和占位符
3. **Markdown语法错误**：容错处理，显示原始文本
4. **并发上传**：通过数据库事务保证一致性
5. **大文件处理**：分块上传，进度显示

## 6. 范围定义

### 6.1 包含功能 (In Scope)

**V1.0核心功能**：
- ✅ 学科创建和管理
- ✅ 文件夹批量上传和结构保留
- ✅ Markdown文件渲染和图片显示
- ✅ 访客免登录浏览
- ✅ 响应式布局适配
- ✅ 内容删除功能（学科和文件）
- ✅ 覆盖式更新功能
- ✅ 基础的错误处理和用户反馈

**技术实现**：
- ✅ Vue3 + TypeScript前端框架
- ✅ Node.js + Koa后端服务
- ✅ SQLite数据库存储
- ✅ Docker容器化部署
- ✅ 基于Vben Admin的管理界面

### 6.2 排除功能 (Out of Scope)

**V1.0不包含的功能**：
- ❌ 用户注册和登录系统
- ❌ 权限管理和角色控制
- ❌ 在线编辑Markdown功能
- ❌ 评论和互动功能
- ❌ 搜索和筛选功能
- ❌ 数据统计和分析功能
- ❌ 多语言支持
- ❌ 主题和样式自定义
- ❌ API接口的外部调用
- ❌ 数据导出功能
- ❌ 版本控制和历史记录
- ❌ 通知和提醒功能
- ❌ 移动端原生应用

**技术限制**：
- ❌ 不支持除SQLite外的其他数据库
- ❌ 不支持分布式部署
- ❌ 不支持CDN加速
- ❌ 不支持HTTPS配置（由运维层面处理）

## 7. 依赖与风险

### 7.1 内外部依赖项

**技术依赖**：
- Vue3生态系统（Vue、Vite、TypeScript）
- Node.js运行环境
- Docker容器化平台
- Nginx反向代理服务

**外部依赖**：
- 服务器硬件资源
- 网络带宽和稳定性
- Docker Hub镜像仓库

**内部依赖**：
- 团队技术栈熟悉度
- 开发和测试环境准备
- 部署流程和运维支持

### 7.2 潜在风险

**技术风险**：
- **高风险**：大文件上传可能导致服务器性能问题
  - 缓解方案：实施文件大小限制，优化上传流程
- **中风险**：并发访问可能导致数据库锁定
  - 缓解方案：优化数据库查询，实施连接池管理
- **中风险**：图片路径替换可能出现错误
  - 缓解方案：充分测试，建立回滚机制

**业务风险**：
- **高风险**：用户误操作导致重要数据丢失
  - 缓解方案：实施二次确认机制，定期数据备份
- **中风险**：访问控制URL泄露导致安全问题
  - 缓解方案：使用环境变量配置，定期更换URL
- **低风险**：用户体验不佳影响使用率
  - 缓解方案：充分的用户测试，持续优化界面

**项目风险**：
- **中风险**：开发周期可能延长
  - 缓解方案：合理的任务分解，定期进度检查
- **低风险**：团队成员技术栈学习成本
  - 缓解方案：提供技术培训，建立知识分享机制

## 8. 发布初步计划

### 8.1 发布策略

**阶段1：内部测试（Week 1-2）**
- 完成核心功能开发
- 内部团队功能测试
- 修复关键bug和性能问题

**阶段2：小范围试用（Week 3）**
- 邀请5-10名目标用户试用
- 收集用户反馈和使用数据
- 优化用户体验和界面设计

**阶段3：正式发布（Week 4）**
- 完成所有功能测试和优化
- 部署到生产环境
- 开放给所有目标用户使用

### 8.2 数据跟踪

**关键指标监控**：
- 系统性能指标：响应时间、可用性、错误率
- 用户行为指标：访问量、上传量、浏览时长
- 业务指标：学科数量、文件数量、用户满意度

**监控工具**：
- 服务器性能监控：系统资源使用情况
- 应用日志分析：错误日志和访问日志
- 用户反馈收集：问题报告和改进建议

**数据分析计划**：
- 每周生成使用情况报告
- 每月进行用户满意度调研
- 根据数据反馈持续优化产品功能

---

**文档状态**: ✅ 已完成
**下一步**: 等待老板审核，准备进入架构设计阶段
**版权声明**: 本文档版权归属于【随影】