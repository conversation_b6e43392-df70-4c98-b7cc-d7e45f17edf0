# 项目初始化文档

## 项目基本信息
- **项目名称**: 期末复习平台 (QiMo Review Platform)
- **项目代号**: qimo
- **创建时间**: 2025-01-27
- **团队领袖**: Mike
- **版权归属**: 随影

## 目录结构说明

### 文档目录 (/docs)
```
docs/
├── prd/              # 产品需求文档目录
│   └── [PRD_项目名_v版本号.md]
├── architecture/     # 系统架构文档目录
│   ├── [Overall_Architecture_项目名.md]
│   └── [Arch_Slice_切片名.md]
├── development/      # 开发技术文档目录
│   ├── API文档
│   ├── 部署指南
│   └── 使用说明
├── analytics/        # 数据分析文档目录
│   └── [Analytics_分析主题_日期.md]
├── tasks/           # 任务规划文档目录
│   └── 任务分解和管理文档
└── templates/       # 文档模板库
    ├── PRD_Template.md
    ├── Architecture_Template.md
    └── Analytics_Template.md
```

### 源码目录 (/src)
- 存放所有源代码文件
- 按功能模块组织目录结构

### 测试目录 (/tests)
- 存放所有测试文件
- 必须集成Playwright自动化测试
- 测试通过后自动清理临时文件

### 配置目录 (/config)
- 存放项目配置文件
- 环境配置、构建配置等

## 团队工作规范

### 文档生成规范
1. **完整性要求**: 所有思考结果必须物化为完整文档
2. **格式标准**: 统一使用Markdown格式
3. **命名规范**: 遵循预定义的文件命名规则
4. **版本管理**: 重要文档需要版本控制

### 工具使用规范
1. **Desktop Commander**: 所有文件操作必须使用DC工具
2. **Playwright**: 所有浏览器相关操作强制使用Playwright
3. **Context7**: 统一开发环境
4. **Sequential Thinking**: 复杂问题分析工具

### 协作流程规范
1. **任务分配**: Mike统一分配和协调
2. **状态汇报**: 完成任务后立即向Mike汇报
3. **文档链接**: 汇报时必须提供完整文档链接
4. **质量门禁**: 每个阶段都有质量检查点

## 项目初始化完成清单
- [x] 创建标准目录结构
- [x] 生成项目README文档
- [x] 创建项目初始化文档
- [x] 建立文档管理体系
- [ ] 等待老板确认项目方向
- [ ] 启动需求分析阶段

## 下一步行动
1. 等待老板明确项目具体需求
2. Emma开始PRD编写工作
3. 建立完整的项目管理体系

---
*文档创建者: Mike*
*创建时间: 2025-01-27*