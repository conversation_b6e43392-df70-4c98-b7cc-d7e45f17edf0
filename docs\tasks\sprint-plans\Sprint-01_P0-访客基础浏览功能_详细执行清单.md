# Sprint-01: P0-访客基础浏览功能 - 详细执行清单

## 文档信息
- **Sprint编号**: Sprint-01
- **切片名称**: P0-访客基础浏览功能
- **任务ID**: `8011ab4a-31d0-4af1-a10a-5da07e885267`
- **创建时间**: 2025-01-27
- **规划人员**: <PERSON> (架构师) + <PERSON> (团队领袖)
- **执行人员**: Alex (工程师)
- **基于文档**: PRD_Final-Review-Platform_v1.0.md, 任务规划_期末复习平台_垂直切片.md, Overall_Architecture_期末复习平台.md
- **版权归属**: 随影

## 1. Sprint概述

### 1.1 用户价值
访客能够浏览学科列表和阅读Markdown笔记内容，实现平台的核心浏览功能。

### 1.2 技术范围
- **数据库层**: 创建subjects表和file_nodes表基础结构
- **后端API层**: 实现GET /api/subjects, GET /api/files/{fileId}, GET /api/subjects/{id}/files三个只读接口
- **前端界面层**: 学科列表页、文件列表页、Markdown阅读页三个核心页面
- **集成测试层**: 端到端的用户浏览流程验证

### 1.3 验收标准
访客通过根路径访问平台，能够浏览学科列表，点击进入学科查看文件，点击文件正常阅读内容。整个流程响应时间符合性能要求（列表加载≤3秒，Markdown渲染≤2秒）。

### 1.4 总体工期
**预估工期**: 5-7个工作日
**里程碑**: 完成MVP的核心浏览功能，为后续切片奠定技术基础

## 2. 子任务详细分解

### 任务1.1: 数据模型设计与实现

#### 基本信息
- **任务编号**: 1.1
- **任务名称**: 数据模型设计与实现
- **负责人**: Alex (工程师)
- **预估工期**: 1个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 无依赖（基础任务）

#### 技术实现要求

**1.1.1 数据库表结构创建**
```sql
-- subjects表（学科表）
CREATE TABLE subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,           -- 学科名称，唯一约束
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- file_nodes表（文件节点表）
CREATE TABLE file_nodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,                -- 所属学科ID
    parent_id INTEGER,                          -- 父节点ID，NULL表示根节点
    name VARCHAR(255) NOT NULL,                 -- 文件/文件夹名称
    type VARCHAR(10) NOT NULL,                  -- 'file' 或 'folder'
    path TEXT NOT NULL,                         -- 相对于学科根目录的路径
    size INTEGER DEFAULT 0,                     -- 文件大小(字节)，文件夹为0
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
);
```

**1.1.2 索引设计**
```sql
-- 基础索引
CREATE INDEX idx_subjects_name ON subjects(name);
CREATE INDEX idx_file_nodes_subject_id ON file_nodes(subject_id);
CREATE INDEX idx_file_nodes_parent_id ON file_nodes(parent_id);
CREATE INDEX idx_file_nodes_path ON file_nodes(path);
CREATE INDEX idx_file_nodes_type ON file_nodes(type);
CREATE UNIQUE INDEX idx_file_nodes_unique_path ON file_nodes(subject_id, path);
```

**1.1.3 数据库连接配置**
- 使用better-sqlite3库，配置同步API模式
- 设置数据库文件路径：`storage/database/app.db`
- 配置连接池参数和事务处理机制
- 实现数据库健康检查功能

**1.1.4 种子数据准备**
```sql
-- 测试用学科数据
INSERT INTO subjects (name) VALUES 
('数据结构'),
('算法设计'),
('操作系统');

-- 测试用文件数据
INSERT INTO file_nodes (subject_id, parent_id, name, type, path, size) VALUES
(1, NULL, '第一章', 'folder', '第一章', 0),
(1, 1, '链表.md', 'file', '第一章/链表.md', 1024),
(1, 1, '栈和队列.md', 'file', '第一章/栈和队列.md', 2048),
(1, NULL, '第二章', 'folder', '第二章', 0),
(1, 4, '树.md', 'file', '第二章/树.md', 1536);
```

#### 交付物清单
1. **数据库初始化脚本**: `src/database/init.sql`
2. **数据库连接模块**: `src/database/connection.js`
3. **数据模型定义**: `src/models/Subject.js`, `src/models/FileNode.js`
4. **种子数据脚本**: `src/database/seeds.sql`
5. **数据库健康检查**: `src/database/health.js`

#### 验收标准
- [ ] 数据库表结构创建成功，所有字段类型和约束正确
- [ ] 索引创建成功，查询性能满足要求（单表查询≤100ms）
- [ ] 外键约束正常工作，级联删除功能验证通过
- [ ] 种子数据插入成功，数据完整性验证通过
- [ ] 数据库连接稳定，支持并发访问（至少10个并发连接）
- [ ] 健康检查接口返回正常状态

#### 技术规范
- 使用better-sqlite3 v8.x版本
- 数据库文件编码：UTF-8
- 事务隔离级别：SERIALIZABLE
- 连接超时设置：30秒
- 查询超时设置：10秒

---

### 任务1.2: 后端核心API开发

#### 基本信息
- **任务编号**: 1.2
- **任务名称**: 后端核心API开发
- **负责人**: Alex (工程师)
- **预估工期**: 2个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务1.1（数据模型设计与实现）

#### 技术实现要求

**1.2.1 项目基础架构搭建**
```javascript
// 项目结构
src/
├── app.js                 // 应用入口
├── routes/               // 路由定义
│   ├── index.js         // 主路由
│   ├── subjects.js      // 学科相关路由
│   └── files.js         // 文件相关路由
├── controllers/         // 控制器层
│   ├── SubjectController.js
│   └── FileController.js
├── services/           // 服务层
│   ├── SubjectService.js
│   └── FileService.js
├── middleware/         // 中间件
│   ├── errorHandler.js
│   ├── logger.js
│   └── validator.js
└── utils/              // 工具函数
    ├── response.js
    └── constants.js
```

**1.2.2 API接口实现**

**接口1: GET /api/subjects - 获取所有学科**
```javascript
// 请求示例
GET /api/subjects

// 响应示例
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "数据结构",
            "created_at": "2025-01-27T10:00:00Z"
        },
        {
            "id": 2,
            "name": "算法设计",
            "created_at": "2025-01-27T10:05:00Z"
        }
    ],
    "meta": {
        "total": 2,
        "timestamp": "2025-01-27T10:10:00Z"
    }
}
```

**接口2: GET /api/subjects/:id/files - 获取学科文件结构**
```javascript
// 请求示例
GET /api/subjects/1/files

// 响应示例
{
    "success": true,
    "data": {
        "subject": {
            "id": 1,
            "name": "数据结构"
        },
        "files": [
            {
                "id": 1,
                "name": "第一章",
                "type": "folder",
                "path": "第一章",
                "children": [
                    {
                        "id": 2,
                        "name": "链表.md",
                        "type": "file",
                        "path": "第一章/链表.md",
                        "size": 1024
                    }
                ]
            }
        ]
    }
}
```

**接口3: GET /api/files/:id - 获取单个文件内容**
```javascript
// 请求示例
GET /api/files/2

// 响应示例
{
    "success": true,
    "data": {
        "id": 2,
        "name": "链表.md",
        "type": "file",
        "path": "第一章/链表.md",
        "size": 1024,
        "content": "# 链表数据结构\n\n链表是一种线性数据结构...",
        "subject": {
            "id": 1,
            "name": "数据结构"
        }
    }
}
```

**1.2.3 错误处理机制**
```javascript
// 统一错误响应格式
{
    "success": false,
    "error": {
        "code": "NOT_FOUND",
        "message": "学科不存在",
        "details": {
            "subject_id": 999
        }
    },
    "meta": {
        "timestamp": "2025-01-27T10:15:00Z",
        "request_id": "req_123456"
    }
}

// 错误码定义
const ERROR_CODES = {
    VALIDATION_ERROR: 400,      // 参数验证错误
    NOT_FOUND: 404,             // 资源不存在
    INTERNAL_ERROR: 500         // 服务器内部错误
};
```

**1.2.4 中间件实现**
- **日志中间件**: 记录请求响应日志，包含请求ID、耗时、状态码
- **错误处理中间件**: 统一错误格式，错误日志记录
- **参数验证中间件**: 输入参数类型和格式验证
- **响应格式中间件**: 统一成功响应格式

#### 交付物清单
1. **Koa应用框架**: `src/app.js`
2. **路由定义**: `src/routes/subjects.js`, `src/routes/files.js`
3. **控制器实现**: `src/controllers/SubjectController.js`, `src/controllers/FileController.js`
4. **服务层逻辑**: `src/services/SubjectService.js`, `src/services/FileService.js`
5. **中间件模块**: `src/middleware/errorHandler.js`, `src/middleware/logger.js`
6. **工具函数**: `src/utils/response.js`, `src/utils/constants.js`
7. **API文档**: `docs/api/README.md`

#### 验收标准
- [ ] 三个API接口功能完整，响应格式符合规范
- [ ] 错误处理机制完善，所有异常情况都有适当的错误响应
- [ ] 参数验证严格，非法输入被正确拦截
- [ ] 日志记录完整，便于问题排查和性能监控
- [ ] API响应时间符合要求（≤500ms）
- [ ] 并发处理能力满足要求（支持50个并发请求）
- [ ] 内存使用稳定，无内存泄漏
- [ ] API文档完整准确，包含请求示例和响应示例

#### 技术规范
- 使用Koa v2.x框架
- 使用koa-router进行路由管理
- 使用koa-bodyparser处理请求体
- 统一使用async/await语法
- 错误处理使用try-catch包装
- 日志格式：JSON结构化日志

---

### 任务1.3: 前端页面骨架搭建

#### 基本信息
- **任务编号**: 1.3
- **任务名称**: 前端页面骨架搭建
- **负责人**: Alex (工程师)
- **预估工期**: 2个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 可与任务1.2并行开发

#### 技术实现要求

**1.3.1 Vue3项目初始化**
```bash
# 使用Vite创建Vue3项目
npm create vue@latest frontend
cd frontend
npm install

# 安装必要依赖
npm install ant-design-vue@4.x
npm install @unocss/vite
npm install vue-router@4.x
npm install axios
npm install markdown-it
npm install highlight.js
```

**1.3.2 项目结构设计**
```
frontend/
├── src/
│   ├── main.ts              // 应用入口
│   ├── App.vue              // 根组件
│   ├── router/              // 路由配置
│   │   └── index.ts
│   ├── views/               // 页面组件
│   │   ├── SubjectList.vue  // 学科列表页
│   │   ├── FileList.vue     // 文件列表页
│   │   └── MarkdownReader.vue // Markdown阅读页
│   ├── components/          // 通用组件
│   │   ├── Layout/
│   │   │   ├── Header.vue
│   │   │   └── Footer.vue
│   │   └── Common/
│   │       ├── Loading.vue
│   │       └── ErrorMessage.vue
│   ├── composables/         // 组合式函数
│   │   ├── useApi.ts
│   │   └── useMarkdown.ts
│   ├── utils/               // 工具函数
│   │   ├── api.ts
│   │   └── constants.ts
│   └── styles/              // 样式文件
│       ├── main.css
│       └── variables.css
├── public/                  // 静态资源
└── index.html              // HTML模板
```

**1.3.3 路由配置**
```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import SubjectList from '@/views/SubjectList.vue'
import FileList from '@/views/FileList.vue'
import MarkdownReader from '@/views/MarkdownReader.vue'

const routes = [
  {
    path: '/',
    name: 'SubjectList',
    component: SubjectList,
    meta: { title: '学科列表' }
  },
  {
    path: '/subjects/:id/files',
    name: 'FileList',
    component: FileList,
    meta: { title: '文件列表' }
  },
  {
    path: '/files/:id',
    name: 'MarkdownReader',
    component: MarkdownReader,
    meta: { title: 'Markdown阅读' }
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

**1.3.4 核心页面组件**

**学科列表页 (SubjectList.vue)**
```vue
<template>
  <div class="subject-list">
    <a-layout>
      <a-layout-header>
        <h1>期末复习平台</h1>
      </a-layout-header>
      <a-layout-content>
        <div class="container">
          <h2>学科列表</h2>
          <a-row :gutter="[16, 16]">
            <a-col 
              v-for="subject in subjects" 
              :key="subject.id"
              :xs="24" :sm="12" :md="8" :lg="6"
            >
              <a-card 
                :title="subject.name"
                hoverable
                @click="goToFileList(subject.id)"
              >
                <p>点击查看学科内容</p>
              </a-card>
            </a-col>
          </a-row>
          <a-empty v-if="subjects.length === 0" description="暂无学科数据" />
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useApi } from '@/composables/useApi'

interface Subject {
  id: number
  name: string
  created_at: string
}

const router = useRouter()
const { fetchSubjects } = useApi()
const subjects = ref<Subject[]>([])

const goToFileList = (subjectId: number) => {
  router.push(`/subjects/${subjectId}/files`)
}

onMounted(async () => {
  try {
    subjects.value = await fetchSubjects()
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
})
</script>
```

**文件列表页 (FileList.vue)**
```vue
<template>
  <div class="file-list">
    <a-layout>
      <a-layout-header>
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/">首页</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ subject?.name }}</a-breadcrumb-item>
        </a-breadcrumb>
      </a-layout-header>
      <a-layout-content>
        <div class="container">
          <h2>{{ subject?.name }} - 文件列表</h2>
          <a-tree
            :tree-data="fileTree"
            :field-names="{ title: 'name', key: 'id', children: 'children' }"
            @select="handleFileSelect"
          >
            <template #title="{ name, type }">
              <span>
                <file-outlined v-if="type === 'file'" />
                <folder-outlined v-else />
                {{ name }}
              </span>
            </template>
          </a-tree>
          <a-empty v-if="fileTree.length === 0" description="暂无文件数据" />
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { FileOutlined, FolderOutlined } from '@ant-design/icons-vue'
import { useApi } from '@/composables/useApi'

const route = useRoute()
const router = useRouter()
const { fetchSubjectFiles } = useApi()

const subject = ref(null)
const fileTree = ref([])

const handleFileSelect = (selectedKeys: string[], { node }: any) => {
  if (node.type === 'file') {
    router.push(`/files/${node.id}`)
  }
}

onMounted(async () => {
  try {
    const subjectId = Number(route.params.id)
    const data = await fetchSubjectFiles(subjectId)
    subject.value = data.subject
    fileTree.value = data.files
  } catch (error) {
    console.error('获取文件列表失败:', error)
  }
})
</script>
```

**Markdown阅读页 (MarkdownReader.vue)**
```vue
<template>
  <div class="markdown-reader">
    <a-layout>
      <a-layout-header>
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/">首页</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link :to="`/subjects/${file?.subject?.id}/files`">
              {{ file?.subject?.name }}
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ file?.name }}</a-breadcrumb-item>
        </a-breadcrumb>
      </a-layout-header>
      <a-layout-content>
        <div class="container">
          <article class="markdown-content">
            <h1>{{ file?.name }}</h1>
            <div v-html="renderedContent" class="markdown-body"></div>
          </article>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useApi } from '@/composables/useApi'
import { useMarkdown } from '@/composables/useMarkdown'

const route = useRoute()
const { fetchFile } = useApi()
const { renderMarkdown } = useMarkdown()

const file = ref(null)

const renderedContent = computed(() => {
  if (!file.value?.content) return ''
  return renderMarkdown(file.value.content)
})

onMounted(async () => {
  try {
    const fileId = Number(route.params.id)
    file.value = await fetchFile(fileId)
  } catch (error) {
    console.error('获取文件内容失败:', error)
  }
})
</script>

<style scoped>
.markdown-body {
  line-height: 1.6;
  font-size: 16px;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3 {
  margin-top: 24px;
  margin-bottom: 16px;
}

.markdown-body p {
  margin-bottom: 16px;
}

.markdown-body pre {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
}
</style>
```

**1.3.5 组合式函数实现**
```typescript
// src/composables/useApi.ts
import { ref } from 'vue'
import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  timeout: 10000
})

export function useApi() {
  const loading = ref(false)
  const error = ref(null)

  const fetchSubjects = async () => {
    loading.value = true
    try {
      const response = await api.get('/subjects')
      return response.data.data
    } catch (err) {
      error.value = err
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchSubjectFiles = async (subjectId: number) => {
    loading.value = true
    try {
      const response = await api.get(`/subjects/${subjectId}/files`)
      return response.data.data
    } catch (err) {
      error.value = err
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchFile = async (fileId: number) => {
    loading.value = true
    try {
      const response = await api.get(`/files/${fileId}`)
      return response.data.data
    } catch (err) {
      error.value = err
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    fetchSubjects,
    fetchSubjectFiles,
    fetchFile
  }
}
```

#### 交付物清单
1. **Vue3项目结构**: 完整的前端项目目录和配置
2. **路由配置**: `src/router/index.ts`
3. **核心页面组件**: `SubjectList.vue`, `FileList.vue`, `MarkdownReader.vue`
4. **布局组件**: `Header.vue`, `Footer.vue`
5. **组合式函数**: `useApi.ts`, `useMarkdown.ts`
6. **样式文件**: 基础样式和响应式布局
7. **构建配置**: Vite配置文件和依赖管理

#### 验收标准
- [ ] 三个核心页面组件功能完整，UI布局合理
- [ ] 路由导航正常，页面间跳转流畅
- [ ] 响应式布局适配不同屏幕尺寸
- [ ] 组件代码结构清晰，符合Vue3最佳实践
- [ ] TypeScript类型定义完整，无类型错误
- [ ] 样式美观，用户体验良好
- [ ] 构建打包成功，无编译错误
- [ ] 代码注释完整，便于维护

#### 技术规范
- 使用Vue3 Composition API
- 使用TypeScript进行类型检查
- 使用Ant Design Vue 4.x组件库
- 使用UnoCSS进行样式管理
- 遵循Vue3官方代码规范
- 组件命名使用PascalCase
- 文件命名使用kebab-case

---

### 任务1.4: 前后端数据联调

#### 基本信息
- **任务编号**: 1.4
- **任务名称**: 前后端数据联调
- **负责人**: Alex (工程师)
- **预估工期**: 1个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务1.2（后端核心API开发）和任务1.3（前端页面骨架搭建）

#### 技术实现要求

**1.4.1 开发环境配置**
```javascript
// 后端开发服务器配置 (src/app.js)
const Koa = require('koa')
const cors = require('@koa/cors')
const app = new Koa()

// 配置CORS，允许前端开发服务器访问
app.use(cors({
  origin: 'http://localhost:5173', // Vite默认端口
  credentials: true
}))

// 启动后端服务器
app.listen(3000, () => {
  console.log('后端服务器启动在 http://localhost:3000')
})
```

```typescript
// 前端API配置 (src/utils/api.ts)
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.data)
    return response
  },
  error => {
    console.error('响应错误:', error.response?.status, error.response?.data)
    return Promise.reject(error)
  }
)

export default api
```

**1.4.2 数据流测试**

**测试场景1: 学科列表加载**
```typescript
// 前端测试代码
const testSubjectList = async () => {
  try {
    console.log('开始测试学科列表加载...')
    const response = await api.get('/subjects')
    
    console.log('API响应:', response.data)
    
    // 验证响应格式
    assert(response.data.success === true, '响应success字段应为true')
    assert(Array.isArray(response.data.data), 'data字段应为数组')
    assert(response.data.data.length > 0, '应该有学科数据')
    
    console.log('✅ 学科列表加载测试通过')
  } catch (error) {
    console.error('❌ 学科列表加载测试失败:', error)
  }
}
```

**测试场景2: 文件列表加载**
```typescript
const testFileList = async (subjectId: number) => {
  try {
    console.log(`开始测试学科${subjectId}的文件列表加载...`)
    const response = await api.get(`/subjects/${subjectId}/files`)
    
    console.log('API响应:', response.data)
    
    // 验证响应格式
    assert(response.data.success === true, '响应success字段应为true')
    assert(response.data.data.subject, '应该包含学科信息')
    assert(Array.isArray(response.data.data.files), 'files字段应为数组')
    
    console.log('✅ 文件列表加载测试通过')
  } catch (error) {
    console.error('❌ 文件列表加载测试失败:', error)
  }
}
```

**测试场景3: 文件内容加载**
```typescript
const testFileContent = async (fileId: number) => {
  try {
    console.log(`开始测试文件${fileId}的内容加载...`)
    const response = await api.get(`/files/${fileId}`)
    
    console.log('API响应:', response.data)
    
    // 验证响应格式
    assert(response.data.success === true, '响应success字段应为true')
    assert(response.data.data.content, '应该包含文件内容')
    assert(response.data.data.type === 'file', '类型应为file')
    
    console.log('✅ 文件内容加载测试通过')
  } catch (error) {
    console.error('❌ 文件内容加载测试失败:', error)
  }
}
```

**1.4.3 错误处理联调**
```typescript
// 前端错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    switch (status) {
      case 404:
        message.error('请求的资源不存在')
        break
      case 500:
        message.error('服务器内部错误，请稍后重试')
        break
      default:
        message.error(data?.error?.message || '请求失败')
    }
  } else if (error.request) {
    // 网络错误
    message.error('网络连接失败，请检查网络设置')
  } else {
    // 其他错误
    message.error('发生未知错误')
  }
}

// 在组合式函数中使用
export function useApi() {
  const fetchSubjects = async () => {
    try {
      const response = await api.get('/subjects')
      return response.data.data
    } catch (error) {
      handleApiError(error)
      throw error
    }
  }
  
  return { fetchSubjects }
}
```

**1.4.4 性能优化**
```typescript
// 请求缓存机制
const cache = new Map()

const fetchWithCache = async (url: string, cacheTime = 5 * 60 * 1000) => {
  const cacheKey = url
  const cached = cache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < cacheTime) {
    console.log('使用缓存数据:', url)
    return cached.data
  }
  
  const response = await api.get(url)
  cache.set(cacheKey, {
    data: response.data,
    timestamp: Date.now()
  })
  
  return response.data
}

// 加载状态管理
const useLoading = () => {
  const loading = ref(false)
  
  const withLoading = async (fn: Function) => {
    loading.value = true
    try {
      return await fn()
    } finally {
      loading.value = false
    }
  }
  
  return { loading, withLoading }
}
```

**1.4.5 用户体验优化**
```vue
<!-- 加载状态显示 -->
<template>
  <div class="subject-list">
    <a-spin :spinning="loading" tip="加载中...">
      <a-row :gutter="[16, 16]">
        <a-col v-for="subject in subjects" :key="subject.id">
          <a-card :title="subject.name" hoverable>
            <p>点击查看学科内容</p>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
    
    <!-- 空状态 -->
    <a-empty v-if="!loading && subjects.length === 0" 
             description="暂无学科数据" />
    
    <!-- 错误状态 -->
    <a-result v-if="error" 
              status="error"
              title="加载失败"
              :sub-title="error.message">
      <template #extra>
        <a-button type="primary" @click="retry">重试</a-button>
      </template>
    </a-result>
  </div>
</template>
```

#### 交付物清单
1. **开发环境配置**: CORS配置、代理设置
2. **API集成代码**: 完整的前端API调用逻辑
3. **错误处理机制**: 统一的错误处理和用户提示
4. **性能优化**: 请求缓存、加载状态管理
5. **测试脚本**: 数据流测试用例
6. **用户体验优化**: 加载状态、空状态、错误状态处理
7. **联调文档**: 前后端接口联调说明

#### 验收标准
- [ ] 前端能够成功调用所有后端API接口
- [ ] 数据在前端页面正确显示，格式符合预期
- [ ] 错误处理机制完善，用户能够得到清晰的错误提示
- [ ] 加载状态显示正常，用户体验良好
- [ ] 网络异常时有适当的重试机制
- [ ] 性能符合要求（页面加载时间≤3秒）
- [ ] 浏览器控制台无错误信息
- [ ] 所有测试用例通过

#### 技术规范
- 使用axios进行HTTP请求
- 统一的错误处理机制
- 合理的加载状态管理
- 适当的请求缓存策略
- 清晰的日志输出
- 符合RESTful API调用规范

---

### 任务1.5: 自动化测试用例编写

#### 基本信息
- **任务编号**: 1.5
- **任务名称**: 自动化测试用例编写
- **负责人**: Alex (工程师)
- **预估工期**: 1个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务1.4（前后端数据联调）

#### 技术实现要求

**1.5.1 测试环境搭建**
```bash
# 安装Playwright测试框架
npm install -D @playwright/test
npm install -D playwright

# 初始化Playwright配置
npx playwright install
```

```javascript
// playwright.config.js
module.exports = {
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  webServer: [
    {
      command: 'npm run dev',
      port: 5173,
      reuseExistingServer: !process.env.CI
    },
    {
      command: 'npm run start:backend',
      port: 3000,
      reuseExistingServer: !process.env.CI
    }
  ]
}
```

**1.5.2 端到端测试用例**

**测试用例1: 完整的用户浏览流程**
```javascript
// tests/e2e/user-browsing-flow.spec.js
const { test, expect } = require('@playwright/test')

test.describe('P0-访客基础浏览功能', () => {
  test('完整的用户浏览流程', async ({ page }) => {
    // 1. 访问首页，查看学科列表
    await page.goto('/')
    
    // 验证页面标题
    await expect(page).toHaveTitle(/期末复习平台/)
    
    // 验证学科列表加载
    await expect(page.locator('h2')).toContainText('学科列表')
    
    // 等待学科卡片加载
    await page.waitForSelector('.ant-card', { timeout: 5000 })
    
    // 验证至少有一个学科
    const subjectCards = page.locator('.ant-card')
    await expect(subjectCards).toHaveCountGreaterThan(0)
    
    // 2. 点击第一个学科，进入文件列表
    const firstSubject = subjectCards.first()
    const subjectName = await firstSubject.locator('.ant-card-head-title').textContent()
    await firstSubject.click()
    
    // 验证URL变化
    await expect(page).toHaveURL(/\/subjects\/\d+\/files/)
    
    // 验证面包屑导航
    await expect(page.locator('.ant-breadcrumb')).toContainText(subjectName)
    
    // 验证文件树加载
    await page.waitForSelector('.ant-tree', { timeout: 5000 })
    
    // 3. 点击第一个Markdown文件
    const markdownFile = page.locator('.ant-tree-node-content-wrapper').filter({
      hasText: '.md'
    }).first()
    
    await markdownFile.click()
    
    // 验证跳转到阅读页面
    await expect(page).toHaveURL(/\/files\/\d+/)
    
    // 验证Markdown内容加载
    await page.waitForSelector('.markdown-content', { timeout: 5000 })
    await expect(page.locator('.markdown-body')).toBeVisible()
    
    // 验证面包屑导航完整
    const breadcrumbs = page.locator('.ant-breadcrumb-item')
    await expect(breadcrumbs).toHaveCountGreaterThanOrEqual(3)
    
    // 4. 测试返回导航
    await page.locator('.ant-breadcrumb-item').nth(1).locator('a').click()
    await expect(page).toHaveURL(/\/subjects\/\d+\/files/)
    
    await page.locator('.ant-breadcrumb-item').first().locator('a').click()
    await expect(page).toHaveURL('/')
  })
  
  test('响应式布局测试', async ({ page }) => {
    // 测试桌面端布局
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.goto('/')
    
    // 验证桌面端学科卡片布局
    const cards = page.locator('.ant-col')
    await expect(cards.first()).toHaveClass(/ant-col-lg-6/)
    
    // 测试平板端布局
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    
    // 验证平板端学科卡片布局
    await expect(cards.first()).toHaveClass(/ant-col-md-8/)
    
    // 测试移动端布局
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    
    // 验证移动端学科卡片布局
    await expect(cards.first()).toHaveClass(/ant-col-xs-24/)
  })
  
  test('错误处理测试', async ({ page }) => {
    // 测试访问不存在的学科
    await page.goto('/subjects/999/files')
    
    // 应该显示错误信息或重定向到首页
    await page.waitForTimeout(2000)
    const hasError = await page.locator('.ant-result-error').isVisible()
    const isRedirected = page.url() === 'http://localhost:5173/'
    
    expect(hasError || isRedirected).toBeTruthy()
    
    // 测试访问不存在的文件
    await page.goto('/files/999')
    
    await page.waitForTimeout(2000)
    const hasFileError = await page.locator('.ant-result-error').isVisible()
    const isFileRedirected = page.url() === 'http://localhost:5173/'
    
    expect(hasFileError || isFileRedirected).toBeTruthy()
  })
})
```

**1.5.3 API接口测试**
```javascript
// tests/api/api.spec.js
const { test, expect } = require('@playwright/test')

test.describe('API接口测试', () => {
  const baseURL = 'http://localhost:3000/api'
  
  test('GET /api/subjects - 获取学科列表', async ({ request }) => {
    const response = await request.get(`${baseURL}/subjects`)
    
    // 验证响应状态
    expect(response.status()).toBe(200)
    
    // 验证响应格式
    const data = await response.json()
    expect(data.success).toBe(true)
    expect(Array.isArray(data.data)).toBe(true)
    expect(data.meta).toBeDefined()
    expect(data.meta.timestamp).toBeDefined()
    
    // 验证学科数据结构
    if (data.data.length > 0) {
      const subject = data.data[0]
      expect(subject.id).toBeDefined()
      expect(subject.name).toBeDefined()
      expect(subject.created_at).toBeDefined()
    }
  })
  
  test('GET /api/subjects/:id/files - 获取学科文件', async ({ request }) => {
    // 先获取学科列表
    const subjectsResponse = await request.get(`${baseURL}/subjects`)
    const subjects = await subjectsResponse.json()
    
    if (subjects.data.length > 0) {
      const subjectId = subjects.data[0].id
      
      const response = await request.get(`${baseURL}/subjects/${subjectId}/files`)
      
      // 验证响应状态
      expect(response.status()).toBe(200)
      
      // 验证响应格式
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.data.subject).toBeDefined()
      expect(Array.isArray(data.data.files)).toBe(true)
      
      // 验证学科信息
      expect(data.data.subject.id).toBe(subjectId)
      expect(data.data.subject.name).toBeDefined()
    }
  })
  
  test('GET /api/files/:id - 获取文件内容', async ({ request }) => {
    // 先获取文件列表
    const subjectsResponse = await request.get(`${baseURL}/subjects`)
    const subjects = await subjectsResponse.json()
    
    if (subjects.data.length > 0) {
      const subjectId = subjects.data[0].id
      const filesResponse = await request.get(`${baseURL}/subjects/${subjectId}/files`)
      const filesData = await filesResponse.json()
      
      // 查找第一个文件
      const findFirstFile = (files) => {
        for (const item of files) {
          if (item.type === 'file') {
            return item
          }
          if (item.children) {
            const found = findFirstFile(item.children)
            if (found) return found
          }
        }
        return null
      }
      
      const firstFile = findFirstFile(filesData.data.files)
      
      if (firstFile) {
        const response = await request.get(`${baseURL}/files/${firstFile.id}`)
        
        // 验证响应状态
        expect(response.status()).toBe(200)
        
        // 验证响应格式
        const data = await response.json()
        expect(data.success).toBe(true)
        expect(data.data.id).toBe(firstFile.id)
        expect(data.data.name).toBeDefined()
        expect(data.data.content).toBeDefined()
        expect(data.data.type).toBe('file')
        expect(data.data.subject).toBeDefined()
      }
    }
  })
  
  test('API错误处理测试', async ({ request }) => {
    // 测试不存在的学科
    const response1 = await request.get(`${baseURL}/subjects/999/files`)
    expect(response1.status()).toBe(404)
    
    const data1 = await response1.json()
    expect(data1.success).toBe(false)
    expect(data1.error).toBeDefined()
    
    // 测试不存在的文件
    const response2 = await request.get(`${baseURL}/files/999`)
    expect(response2.status()).toBe(404)
    
    const data2 = await response2.json()
    expect(data2.success).toBe(false)
    expect(data2.error).toBeDefined()
  })
})
```

**1.5.4 性能测试**
```javascript
// tests/performance/performance.spec.js
const { test, expect } = require('@playwright/test')

test.describe('性能测试', () => {
  test('页面加载性能测试', async ({ page }) => {
    // 测试首页加载时间
    const startTime = Date.now()
    await page.goto('/')
    await page.waitForSelector('.ant-card', { timeout: 5000 })
    const homeLoadTime = Date.now() - startTime
    
    console.log(`首页加载时间: ${homeLoadTime}ms`)
    expect(homeLoadTime).toBeLessThan(3000) // 3秒内加载完成
    
    // 测试文件列表页加载时间
    const firstCard = page.locator('.ant-card').first()
    const listStartTime = Date.now()
    await firstCard.click()
    await page.waitForSelector('.ant-tree', { timeout: 5000 })
    const listLoadTime = Date.now() - listStartTime
    
    console.log(`文件列表页加载时间: ${listLoadTime}ms`)
    expect(listLoadTime).toBeLessThan(3000)
    
    // 测试Markdown页面加载时间
    const markdownFile = page.locator('.ant-tree-node-content-wrapper').filter({
      hasText: '.md'
    }).first()
    
    const markdownStartTime = Date.now()
    await markdownFile.click()
    await page.waitForSelector('.markdown-body', { timeout: 5000 })
    const markdownLoadTime = Date.now() - markdownStartTime
    
    console.log(`Markdown页面加载时间: ${markdownLoadTime}ms`)
    expect(markdownLoadTime).toBeLessThan(2000) // 2秒内渲染完成
  })
  
  test('并发访问测试', async ({ browser }) => {
    const contexts = []
    const pages = []
    
    // 创建10个并发页面
    for (let i = 0; i < 10; i++) {
      const context = await browser.newContext()
      const page = await context.newPage()
      contexts.push(context)
      pages.push(page)
    }
    
    // 并发访问首页
    const startTime = Date.now()
    const promises = pages.map(page => 
      page.goto('/').then(() => page.waitForSelector('.ant-card'))
    )
    
    await Promise.all(promises)
    const totalTime = Date.now() - startTime
    
    console.log(`10个并发页面加载总时间: ${totalTime}ms`)
    expect(totalTime).toBeLessThan(10000) // 10秒内完成
    
    // 清理资源
    for (const context of contexts) {
      await context.close()
    }
  })
})
```

**1.5.5 测试数据管理**
```javascript
// tests/fixtures/test-data.js
const testData = {
  subjects: [
    {
      id: 1,
      name: '数据结构',
      created_at: '2025-01-27T10:00:00Z'
    },
    {
      id: 2,
      name: '算法设计',
      created_at: '2025-01-27T10:05:00Z'
    }
  ],
  
  files: [
    {
      id: 1,
      subject_id: 1,
      name: '第一章',
      type: 'folder',
      path: '第一章',
      children: [
        {
          id: 2,
          subject_id: 1,
          name: '链表.md',
          type: 'file',
          path: '第一章/链表.md',
          size: 1024,
          content: '# 链表数据结构\n\n链表是一种线性数据结构...'
        }
      ]
    }
  ]
}

module.exports = testData
```

#### 交付物清单
1. **Playwright配置**: `playwright.config.js`
2. **端到端测试**: `tests/e2e/user-browsing-flow.spec.js`
3. **API接口测试**: `tests/api/api.spec.js`
4. **性能测试**: `tests/performance/performance.spec.js`
5. **测试数据**: `tests/fixtures/test-data.js`
6. **测试脚本**: `package.json`中的测试命令
7. **测试报告**: HTML格式的测试结果报告
8. **测试文档**: 测试用例说明和执行指南

#### 验收标准
- [ ] 所有端到端测试用例通过，覆盖完整的用户浏览流程
- [ ] API接口测试覆盖所有核心接口，包括正常和异常情况
- [ ] 性能测试验证页面加载时间符合要求
- [ ] 响应式布局测试在不同设备上通过
- [ ] 错误处理测试验证异常情况的处理
- [ ] 测试覆盖率达到80%以上
- [ ] 测试报告清晰，便于问题定位
- [ ] 测试可以在CI/CD环境中自动执行

#### 技术规范
- 使用Playwright进行端到端测试
- 测试用例命名清晰，描述准确
- 测试数据独立，不依赖外部环境
- 测试断言明确，错误信息清晰
- 测试执行稳定，减少偶发性失败
- 测试报告包含截图和执行轨迹

## 3. Sprint执行计划

### 3.1 任务依赖关系
```
任务1.1 (数据模型设计与实现) [1天]
    ↓
任务1.2 (后端核心API开发) [2天] ←→ 任务1.3 (前端页面骨架搭建) [2天]
    ↓                                    ↓
    └─────────→ 任务1.4 (前后端数据联调) [1天] ←─────────┘
                        ↓
                任务1.5 (自动化测试用例编写) [1天]
```

### 3.2 时间安排
- **第1天**: 任务1.1 - 数据模型设计与实现
- **第2-3天**: 任务1.2和1.3并行 - 后端API开发 + 前端页面搭建
- **第4-5天**: 任务1.2和1.3完成 - 后端API开发 + 前端页面搭建
- **第6天**: 任务1.4 - 前后端数据联调
- **第7天**: 任务1.5 - 自动化测试用例编写

### 3.3 质量检查点
- **每日代码审查**: 确保代码质量和规范性
- **中期集成测试**: 第4天进行中期集成验证
- **最终验收测试**: 第7天进行完整功能验收

### 3.4 风险缓解
- **技术风险**: 预留1天缓冲时间处理技术难题
- **集成风险**: 提前进行接口联调，避免最后阶段问题
- **性能风险**: 在开发过程中持续监控性能指标

## 4. 验收标准总结

### 4.1 功能验收
- [ ] 访客能够正常浏览学科列表
- [ ] 访客能够查看学科下的文件结构
- [ ] 访客能够阅读Markdown文件内容
- [ ] 页面导航和面包屑功能正常
- [ ] 响应式布局在不同设备上正常显示

### 4.2 技术验收
- [ ] 数据库表结构正确，数据完整性保证
- [ ] API接口功能完整，响应格式统一
- [ ] 前端组件结构清晰，代码规范
- [ ] 前后端集成无问题，数据流畅通
- [ ] 自动化测试覆盖率达标，测试通过

### 4.3 性能验收
- [ ] 首页加载时间 ≤ 3秒
- [ ] Markdown渲染时间 ≤ 2秒
- [ ] API响应时间 ≤ 500ms
- [ ] 支持50个并发用户访问
- [ ] 内存使用稳定，无内存泄漏

### 4.4 用户体验验收
- [ ] 界面美观，交互流畅
- [ ] 错误提示清晰，用户友好
- [ ] 加载状态明确，用户不会困惑
- [ ] 移动端体验良好，触摸操作便捷

## 5. 交付清单

### 5.1 代码交付物
- [ ] 完整的后端API服务代码
- [ ] 完整的前端Vue3应用代码
- [ ] 数据库初始化和种子数据脚本
- [ ] 完整的自动化测试套件

### 5.2 文档交付物
- [ ] API接口文档
- [ ] 前端组件使用文档
- [ ] 数据库设计文档
- [ ] 测试用例文档
- [ ] 部署和运行指南

### 5.3 配置交付物
- [ ] 开发环境配置文件
- [ ] 构建和打包配置
- [ ] 测试环境配置
- [ ] 代码质量检查配置

---

**文档状态**: ✅ 已完成
**下一步**: 等待老板审核，准备启动Sprint-01开发工作
**版权声明**: 本文档版权归属于【随影】

*详细执行清单完成时间: 2025-01-27*
*规划人员: Bob (架构师) + Mike (团队领袖)*